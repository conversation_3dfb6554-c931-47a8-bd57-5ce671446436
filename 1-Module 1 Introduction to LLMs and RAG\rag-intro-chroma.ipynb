# !wget https://raw.githubusercontent.com/alexeygrigorev/minsearch/main/minsearch.py

# import minsearch

import json

with open('documents.json', 'rt') as f_in:
    docs_raw = json.load(f_in)

documents = []

for course_dict in docs_raw:
    for doc in course_dict['documents']:
        doc['course'] = course_dict['course']
        documents.append(doc)

documents[0]

# index = minsearch.Index(
#     text_fields=["question", "text", "section"],
#     keyword_fields=["course"]
# )

# Install ChromaDB if not already installed
!pip install chromadb --quiet

import chromadb

# Prepare documents for ChromaDB
documents_chroma = []
metadatas = []
ids = []
for i, doc in enumerate(documents):
    documents_chroma.append(f"section: {doc['section']}\nquestion: {doc['question']}\nanswer: {doc['text']}")
    metadatas.append({"course": doc['course'], "section": doc['section'], "question": doc['question']})
    ids.append(f"doc_{i}")

# Set up ChromaDB client and collection
client = chromadb.Client()
collection = client.create_collection("course-faqs")

# Add documents to ChromaDB
collection.add(
    documents=documents_chroma,
    metadatas=metadatas,
    ids=ids
)

def chroma_search(query, course="data-engineering-zoomcamp", n_results=5):
    results = collection.query(
        query_texts=[query],
        n_results=n_results,
        where={"course": course}
    )
    docs = []
    for doc, meta in zip(results['documents'][0], results['metadatas'][0]):
        docs.append({**meta, 'text': doc})
    return docs

q = 'the course has already started, can I still enroll?'

# index.fit(documents)

import os
from dotenv import load_dotenv
load_dotenv()


from openai import OpenAI

client = OpenAI()

response = client.chat.completions.create(
    model='gpt-4o-mini',
    messages=[{"role": "user", "content": q}]
)

response.choices[0].message.content

def search(query):
    boost = {'question': 3.0, 'section': 0.5}

    results = index.search(
        query=query,
        filter_dict={'course': 'data-engineering-zoomcamp'},
        boost_dict=boost,
        num_results=5
    )

    return results

def build_prompt(query, search_results):
    prompt_template = """
You're a course teaching assistant. Answer the QUESTION based on the CONTEXT from the FAQ database.
Use only the facts from the CONTEXT when answering the QUESTION.

QUESTION: {question}

CONTEXT: 
{context}
""".strip()

    context = ""
    
    for doc in search_results:
        context = context + f"section: {doc['section']}\nquestion: {doc['question']}\nanswer: {doc['text']}\n\n"
    
    prompt = prompt_template.format(question=query, context=context).strip()
    return prompt

def llm(prompt):
    response = client.chat.completions.create(
        model='gpt-4o-mini',
        messages=[{"role": "user", "content": prompt}]
    )
    
    return response.choices[0].message.content

# query = 'how do I run kafka?'

# def rag(query):
#     search_results = search(query)
#     prompt = build_prompt(query, search_results)
#     answer = llm(prompt)
#     return answer

query = 'how do I run kafka?'

def rag(query):
    search_results = chroma_search(query)
    prompt = build_prompt(query, search_results)
    answer = llm(prompt)
    return answer

rag(query)

rag('the course has already started, can I still enroll?')

documents[0]

from elasticsearch import Elasticsearch

es_client = Elasticsearch('http://localhost:9200') 

index_settings = {
    "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 0
    },
    "mappings": {
        "properties": {
            "text": {"type": "text"},
            "section": {"type": "text"},
            "question": {"type": "text"},
            "course": {"type": "keyword"} 
        }
    }
}

index_name = "course-questions"

es_client.indices.create(index=index_name, body=index_settings)

documents[0]

from tqdm.auto import tqdm

for doc in tqdm(documents):
    es_client.index(index=index_name, document=doc)

query = 'I just disovered the course. Can I still join it?'

def elastic_search(query):
    search_query = {
        "size": 5,
        "query": {
            "bool": {
                "must": {
                    "multi_match": {
                        "query": query,
                        "fields": ["question^3", "text", "section"],
                        "type": "best_fields"
                    }
                },
                "filter": {
                    "term": {
                        "course": "data-engineering-zoomcamp"
                    }
                }
            }
        }
    }

    response = es_client.search(index=index_name, body=search_query)
    
    result_docs = []
    
    for hit in response['hits']['hits']:
        result_docs.append(hit['_source'])
    
    return result_docs

def rag(query):
    search_results = elastic_search(query)
    prompt = build_prompt(query, search_results)
    answer = llm(prompt)
    return answer

rag(query)

query = 'how do I run kafka?'
print(rag(query))

# Install ChromaDB
!pip install chromadb

# Import ChromaDB and set up client
import chromadb
client = chromadb.Client()

# Create a collection
collection = client.create_collection("all-my-documents")

# Add documents
collection.add(
    documents=["This is document1", "This is document2"],
    metadatas=[{"source": "notion"}, {"source": "google-docs"}],
    ids=["doc1", "doc2"],
)

# Example query
results = collection.query(
    query_texts=["This is a query document"],
    n_results=2
)

print(results)
